# ------------------------------------------------------------
#  TODO:
#  - Add option to change drive letter
# ------------------------------------------------------------


#------------- 1. Global FZF options --------------------------
$env:FZF_DEFAULT_OPTS = @"
--cycle
--scroll-off=5
--border
--preview-window=right,60%,border-left
--bind alt-up:preview-half-page-up
--bind alt-down:preview-half-page-down
--bind alt-right:preview-page-down
--bind alt-left:preview-page-up
--bind ctrl-h:preview-bottom
--bind alt-w:toggle-preview-wrap
--bind ctrl-e:toggle-preview
"@

#------------- 2. Key bindings --------------------------------

# ALT+F  → call fdg directly with current-token query
Set-PSReadLineKeyHandler -Key 'Alt+f' -ScriptBlock {
    
	$q = _fzf_get_initialQueryFromBuffer          # '' if none
	# clear whatever text is on the command line
	[Microsoft.PowerShell.PSConsoleReadLine]::DeleteLineToFirstChar($null, $null)
	fdg -initialQuery $q
}

# ALT+G  → call rgg directly
Set-PSReadLineKeyHandler -Key 'Alt+g' -ScriptBlock {

	$q = _fzf_get_initialQueryFromBuffer
	# clear whatever text is on the command line
	[Microsoft.PowerShell.PSConsoleReadLine]::DeleteLineToFirstChar($null, $null)
	rgg -initialQuery $q
}


#------------- 3. Public wrappers ------------------------------
function fdg([string]$initialQuery = '') {
	_fzf_open_path $(_fzf_get_path_using_fd -initialQuery $initialQuery)
}

function rgg([string]$initialQuery = '') {
	_fzf_open_path $(_fzf_get_path_using_rg -initialQuery $initialQuery)
}


#------------- 4. Ripgrep wrapper (`frg`) ----------------------
function frg([string]$searchString = '') {
	Invoke-PsFzfRipgrep -SearchString $searchString
}

#------------- 5. File-picker via fd → fzf --------------------
function _fzf_get_path_using_fd([string]$initialQuery = '') {

	$selection = fd --type file --hidden --follow --no-ignore --exclude .git "" . 2>$null |
		fzf --multi `
			--prompt 'Files> ' `
			--query "$initialQuery" `
			--header-first `
			--header 'ALT-F: switch (files/dirs) │ ALT-G: switch (local/global)' `
			--bind 'esc:cancel' `
			--bind 'alt-f:transform:if /I "%FZF_PROMPT:~0,5%"=="Files" (echo ^change-prompt^(Directory^> ^)^+^reload^(fd --type directory --hidden --follow --no-ignore --exclude .git "" .^)) else (echo ^change-prompt^(Files^> ^)^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git "" .^))' `
			--bind 'alt-g:transform:if /I "%FZF_PROMPT:~0,6%"=="Global" ( if /I "%FZF_PROMPT:~7,5%"=="Files" (echo ^change-prompt^(Files^> ^)^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git "" .^)) else (echo ^change-prompt^(Directory^> ^)^+^reload^(fd --type directory --hidden --follow --no-ignore --exclude .git "" .^)) ) else ( if /I "%FZF_PROMPT:~0,5%"=="Files" (echo ^change-prompt^(Global ^Files^> ^)^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git "" \^)) else (echo ^change-prompt^(Global ^Directory^> ^)^+^reload^(fd --type directory --hidden --follow --no-ignore --exclude .git "" \^)) )' `
			--preview 'if /I "%FZF_PROMPT:Directory=%"=="%FZF_PROMPT%" (bat --color=always {} --style=numbers) else (eza -T --colour=always --icons=always {})'

	if ($selection) { return $selection }
}


#------------- 6. Ripgrep picker via fzf ----------------------
function _fzf_get_path_using_rg([string]$initialQuery = '') {
	$input_path = "" |
		fzf --ansi --disabled --query "$initialQuery" `
			--bind "start:reload:rg --column --line-number --no-heading --color=always --smart-case {q} . 2>NUL || (exit 0)" `
			--bind "change:reload:rg --column --line-number --no-heading --color=always --smart-case {q} . 2>NUL || (exit 0)" `
			--color 'hl:-1:underline,hl+:-1:underline:reverse' `
			--delimiter ':' `
			--prompt '1. ripgrep> ' `
			--preview-label 'Preview' `
			--header-first `
			--header 'TAB: switch ripgrep/fzf │ ALT-G: switch local/global' `
			--bind 'esc:cancel' `
			--bind 'tab:transform-query:echo {q}' `
			--bind 'tab:transform:if /I "%FZF_PROMPT%"=="2. fzf> " (echo ^change-prompt^(1. ripgrep^> ^)^+^disable-search^+^rebind^(change^)^+^reload^(rg --column --line-number --no-heading --color=always --smart-case {q} .^)) else if /I "%FZF_PROMPT%"=="Global 2. fzf> " (echo ^change-prompt^(Global 1. ripgrep^> ^)^+^disable-search^+^rebind^(change^)^+^reload^(rg --column --line-number --no-heading --color=always --smart-case {q} C:\^)) else if /I "%FZF_PROMPT%"=="1. ripgrep> " (echo ^unbind^(change^)^+^change-prompt^(2. fzf^> ^)^+^enable-search^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git {q} .^)) else (echo ^unbind^(change^)^+^change-prompt^(Global 2. fzf^> ^)^+^enable-search^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git {q} C:\^))' `
			--bind 'alt-g:transform-query:echo {q}' `
			--bind 'alt-g:transform:if /I "%FZF_PROMPT%"=="1. ripgrep> " (echo ^change-prompt^(Global 1. ripgrep^> ^)^+^reload^(rg --column --line-number --no-heading --color=always --smart-case {q} C:\^)) else if /I "%FZF_PROMPT%"=="2. fzf> " (echo ^change-prompt^(Global 2. fzf^> ^)^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git {q} C:\^)) else if /I "%FZF_PROMPT%"=="Global 1. ripgrep> " (echo ^change-prompt^(1. ripgrep^> ^)^+^reload^(rg --column --line-number --no-heading --color=always --smart-case {q} .^)) else (echo ^change-prompt^(2. fzf^> ^)^+^reload^(fd --type file --hidden --follow --no-ignore --exclude .git {q} .^))' `
			--preview 'bat --color=always --style=numbers --highlight-line {2} --line-range {2}: {1} 2>nul || type {1}' `
			--preview-window 'up,60%,border-bottom' 2>$null

	if ($input_path) {
		# Remove trailing line and column numbers so we return only the file path
		$input_path = ($input_path -replace ':(\d+)(:\d+)?$', '').Trim()
		return $input_path
	}
}

#------------- 7. Seed initial query from console buffer -------
function _fzf_get_initialQueryFromBuffer {
	# Use the ref-string overload to retrieve current buffer and cursor
	[string]$buffer = ''
	[int]   $position = 0
	[Microsoft.PowerShell.PSConsoleReadLine]::GetBufferState([ref]$buffer, [ref]$position)

	if ($position -gt 0) {
		$beforeCaret = $buffer.Substring(0, $position)
		return ($beforeCaret -split '\s+')[-1]
	}
	''
}


#------------- 8. Path dispatcher (`_fzf_open_path`) ----------
function _fzf_open_path {
	param (
		[Parameter(Mandatory = $false)]
		[string]$input_path
	)

	# Early return if no path provided
	if ([string]::IsNullOrEmpty($input_path)) {
		return
	}

	# Store original working directory for relative path calculation
	$originalLocation = Get-Location

	# Verify the path exists on disk
	if (-not (Test-Path -LiteralPath $input_path)) {
		Write-Warning "Path not found: $input_path"
		return
	}

	# Get file/path information for operations
	$isDirectory = Test-Path -LiteralPath $input_path -PathType Container
	$fileName = [System.IO.Path]::GetFileName($input_path)
	$fullPath = (Resolve-Path -LiteralPath $input_path).Path
    
	# Calculate relative path
	$relativePath = $fullPath
	try {
		$relativePath = [System.IO.Path]::GetRelativePath($originalLocation, $fullPath)
	}
	catch {
		# Fallback for older PowerShell versions
		if ($fullPath.StartsWith($originalLocation)) {
			$relativePath = $fullPath.Substring($originalLocation.Length).TrimStart('\')
		}
	}

	# Build a hashtable of command → scriptblock
	$cmds = @{
		# Viewing commands
		'bat'         = { 
			bat --color=always --paging=never -- $input_path | Out-Host
		}
		'cat'         = {
			try {
				# Display the file content
				Get-Content -LiteralPath $input_path | Out-Host
        
				# Add a pause so user can read the content
				Write-Host "`nPress any key to continue..." -ForegroundColor Cyan -NoNewline
				$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
				Write-Host "`n`n`n" # Add a newline after key press
			}
			catch {
				Write-Host "Error displaying file: $_" -ForegroundColor Red
			}
		}
        
		# Navigation commands
		'cd'          = {
			if (-not $isDirectory) {
				$parent = Split-Path -Path $input_path -Parent
				Set-Location -LiteralPath $parent
				Write-Host "Changed directory to: $parent" -ForegroundColor Green
			}
			else {
				Set-Location -LiteralPath $input_path
				Write-Host "Changed directory to: $input_path" -ForegroundColor Green
			}
		}
        
		# Editor commands
		'code'        = { 
			Start-Process -FilePath 'code' -ArgumentList $input_path 
		}
		'nvim'        = { 
			nvim $input_path
		}
		'subl'        = {
			Start-Process -FilePath 'subl' -ArgumentList $input_path
		}
		'notepad'     = {
			notepad $input_path
		}
		'ed'          = {
			try {
				$absolutePath = (Resolve-Path -LiteralPath $input_path).Path
        
				Write-Host "Starting edit.exe..." -ForegroundColor Cyan
        
				# Use Start-Process with -Wait to properly handle console applications
				Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "edit.exe", "`"$absolutePath`"" -NoNewWindow -Wait
        
				Write-Host "Completed edit session for: $input_path" -ForegroundColor Green
			}
			catch {
				Write-Host "Error launching edit.exe: $_" -ForegroundColor Red
			}
		}
        
		# Clipboard commands
		'cp-name'     = {
			$fileName | Set-Clipboard
			Write-Host "Copied filename to clipboard: $fileName" -ForegroundColor Green
		}
		'cp-path'     = {
			$fullPath | Set-Clipboard
			Write-Host "Copied full path to clipboard: $fullPath" -ForegroundColor Green
		}
		'cp-rel-path' = {
			# Format the path with .\ prefix if needed
			$formattedPath = if ($relativePath -ne "." -and !$relativePath.StartsWith(".\") -and !$relativePath.StartsWith("..")) {
				".\$relativePath"
			}
			else {
				$relativePath
			}
			$formattedPath | Set-Clipboard
			Write-Host "Copied relative path to clipboard: $formattedPath" -ForegroundColor Green
		}
        
		# File operations
		'open'        = {
			Invoke-Item -Path $input_path
			Write-Host "Opened with default application: $input_path" -ForegroundColor Cyan
		}
		'explorer'    = {
			Start-Process -FilePath "explorer.exe" -ArgumentList "/select,`"$fullPath`""
			Write-Host "Showing in File Explorer: $input_path" -ForegroundColor Cyan
		}
		'remove'      = {
			$confirm = Read-Host "Are you sure you want to delete '$input_path'? (y/N)"
			if ($confirm -eq "y") {
				Remove-Item -LiteralPath $input_path -Recurse -Force
				Write-Host "Deleted: $input_path" -ForegroundColor Yellow
			}
			else {
				Write-Host "Deletion cancelled" -ForegroundColor Cyan
			}
		}
		'git-status'  = {
			try {
				# First check if git is available
				$gitVersion = & git --version 2>$null
				if ($LASTEXITCODE -ne 0) {
					Write-Host "Git command not found in PATH" -ForegroundColor Red
					return
				}
        
				# Get the directory containing the file
				$fileDir = Split-Path -Parent $fullPath
        
				# Check if the path is inside a git repository
				$gitRoot = & git -C $fileDir rev-parse --show-toplevel 2>$null
				if ($LASTEXITCODE -ne 0) {
					Write-Host "Not in a git repository: $input_path" -ForegroundColor Yellow
					return
				}
        
				# Normalize paths for consistency
				$gitRoot = $gitRoot -replace '\\', '/'
				$normalizedFullPath = $fullPath -replace '\\', '/'
        
				# Calculate repository-relative path correctly
				$repoRelativePath = $normalizedFullPath
				if ($normalizedFullPath.StartsWith($gitRoot, [StringComparison]::OrdinalIgnoreCase)) {
					$repoRelativePath = $normalizedFullPath.Substring($gitRoot.Length).TrimStart('/')
				}
        
				# Clear screen before output for better readability
				Clear-Host
        
				# Header section
				Write-Host "Git Status for: $input_path" -ForegroundColor Cyan
				Write-Host "Repository root: $gitRoot" -ForegroundColor DarkCyan
				Write-Host "Repository-relative path: $repoRelativePath" -ForegroundColor DarkCyan
				Write-Host "----------------------------------------" -ForegroundColor DarkGray
        
				# Change to the git root directory temporarily
				Push-Location $gitRoot
        
				try {
					# 1. File status section
					Write-Host "`nFile Status:`n" -ForegroundColor Cyan
					$status = & git status --short -- "$repoRelativePath" 2>&1
					if ($status -and $status -ne "") {
						Write-Host $status
					}
					else {
						Write-Host "(No changes)" -ForegroundColor DarkGray
					}
            
					# 2. Diff section (if changes exist)
					$diffExists = & git diff --quiet -- "$repoRelativePath"
					if ($LASTEXITCODE -ne 0) {
						Write-Host "`nDiff:`n" -ForegroundColor Cyan
						& git diff --stat -- "$repoRelativePath"
                
						# Show just the first few lines of the diff
						$diffOutput = & git diff --unified=3 --color -- "$repoRelativePath"
						if ($diffOutput) {
							$diffLines = $diffOutput -split "`n" | Select-Object -First 20
							$diffLines | ForEach-Object { Write-Host $_ }
						}
					}
            
					# 3. Enhanced commit history section - shows last 10 commits with details
					Write-Host "`nCommit History:`n" -ForegroundColor Cyan
					$commits = & git log -n 10 --pretty=format:"%h %ad %an %s" --date=format:"%Y-%m-%d %H:%M" -- "$repoRelativePath"
					if ($commits) {
						$commits | ForEach-Object {
							if ($_ -match '^(\S+)\s+(\S+\s+\S+)\s+(.+?)\s+(.+)$') {
								# Format: hash date+time author message
								$hash = $matches[1]
								$date = $matches[2]
								$author = $matches[3]
								$message = $matches[4]
            
								# Truncate message if needed (first 100 chars)
								if ($message.Length -gt 100) {
									$message = $message.Substring(0, 97) + "..."
								}
            
								# Colorize output
								Write-Host "$hash " -ForegroundColor Yellow -NoNewline
								Write-Host "$date " -ForegroundColor Cyan -NoNewline
								Write-Host "$author " -ForegroundColor Green -NoNewline
								Write-Host "$message" -ForegroundColor White
							}
							else {
								# Fallback if regex doesn't match
								Write-Host $_
							}
						}
					}
					else {
						Write-Host "(No commit history found for this file)" -ForegroundColor DarkGray
					}
				}
				finally {
					# Restore original location
					Pop-Location
				}
        
				Write-Host "`n`nPress any key to continue..." -ForegroundColor Cyan -NoNewline
				$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
				Write-Host ""
			}
			catch {
				Write-Host "Error executing git commands: $_" -ForegroundColor Red
				Write-Host "`nPress any key to continue..." -ForegroundColor Cyan -NoNewline
				$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
				Write-Host ""
			}
		}
		'echo'        = { Write-Output $input_path }
	}

	# Create command list for fzf
	$actionsList = @(
		"open       - Open with default app"
		"code       - Open in VS Code"
		"subl       - Open in Sublime"
		"notepad    - Open in Notepad"
		"ed         - Open in Windows Edit"
		"nvim       - Open in Neovim"
		"cat        - View content"
		"bat        - Preview with bat"
		"cd         - Change directory to location"
		"explorer   - Show in File Explorer"
		"cp-name    - Copy filename to clipboard"
		"cp-path    - Copy full path to clipboard"
		"cp-rel-path - Copy relative path"
		"remove     - Delete file"
		"git-status - Show git status for file"
		"echo       - Output path"
	)

	# Run fzf with simplified command list
	$selectionLine = $actionsList | 
		Out-String | 
		fzf --prompt="Select action> " `
			--height=15 `
			--header="File: $($fullPath)" `
			--border=rounded `
			--pointer="▶" `
			--marker="✓" `
			--info=inline `
			--color="prompt:cyan,header:blue,pointer:yellow"

	# Extract command name from selection (everything before the first space)
	$selection = if ($selectionLine) { 
		$selectionLine -split ' ' | Select-Object -First 1 
	}
	else {
		return  # Exit if no selection was made
	}

	# Announce and execute
	Write-Host "Executing: $selection $input_path" -ForegroundColor Cyan
	& $cmds[$selection]
}


#------------- 9. Directory-only search (`fz`) ----------------
function fz([string]$Query) {
	$dir = fd -t d --hidden --follow --no-ignore -a $Query 2>$null |
		Invoke-Fzf | Select-Object -First 1

	if ($dir) {
		Set-Location $dir
	}
	else {
		Write-Warning 'No matching directory found.'
	}
}

#region Help

#------------- 10. Quick-reference Help -----------------------
function fzfHelp(
	[ValidateSet('summary', 'detailed')][string]$mode = 'summary',
	[switch]$showKeyBindings) {
	$summary = @"
PSFzf integrates the *fzf* fuzzy-finder into PowerShell.  Core features:

    Alt+f    fuzzy-select a path starting at the cursor or CWD
    Alt+r    fuzzy-search command history
    Alt+c    fuzzy-select a directory and `Set-Location`
    fz       alias for directory-only search
    fkill    alias for process killer
    Tab      optional tab-completion powered by fzf
"@

	$detailed = @"
    'fzf'            : Versatile fuzzy finder for files/dirs.  Example → 'fzf docu'
    'Alt+f'          : Context-aware search in CWD or cursor path. Example → 'Alt+f config'
    'Alt+r'          : Reverse-search PSReadLine history.
    'fz'             : Directory-only search (Invoke-FuzzySetZLocation). Example → 'fz src'
    'Alt+c'          : Interactive tree-style directory explorer.
    'z' (zLocation)  : Jump to recently visited dirs using history.
    'fkill'          : Fuzzy-kill a process. Example → 'fkill chrome'
"@

	if ($mode -eq 'summary') {
		Write-Host "`n$summary"
	}
	else {
		Write-Host "`nPSFzf Navigation Help" -ForegroundColor Cyan
		Write-Host $detailed
		Write-Host "`nExample scenarios:`n" -ForegroundColor Green
		Write-Host "  1. Find a file: Alt+f styles.css"
		Write-Host "  2. Jump to Documents: fz docu"
		Write-Host "  3. Explore tree: Alt+c → Archive"
		Write-Host "  4. Return to recent dir: z script"
	}

	if ($showKeyBindings) {
		Write-Host "`nCurrent Fzf-related key bindings:`n"
		Get-PSReadLineKeyHandler |
			Where-Object Function -like '*Fzf*' |
			Format-Table -AutoSize
	}
}

# endregion Help