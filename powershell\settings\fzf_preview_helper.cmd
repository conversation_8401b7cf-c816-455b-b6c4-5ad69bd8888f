@echo off
setlocal enabledelayedexpansion

rem Parse ripgrep output format: filepath:line:column:content
rem Handle Windows drive letters properly

set "input=%~1"
if "%input%"=="" exit /b 1

rem Try to match pattern: drive:\path\file.ext:line:column:content
for /f "tokens=1,2,3* delims=:" %%a in ("%input%") do (
    set "part1=%%a"
    set "part2=%%b"
    set "part3=%%c"
    set "rest=%%d"
    
    rem Check if part1 is a single letter (drive letter)
    if "!part1:~1!"=="" (
        rem This is a drive letter, reconstruct the path
        set "filepath=!part1!:!part2!"
        set "linenum=!part3!"
    ) else (
        rem This is not a Windows absolute path, treat as relative
        set "filepath=!part1!"
        set "linenum=!part2!"
    )
)

rem Check if file exists and show preview
if exist "%filepath%" (
    if "%linenum%" neq "" (
        bat --color=always --style=numbers --highlight-line %linenum% --line-range %linenum%: "%filepath%" 2>nul || bat --color=always --style=numbers "%filepath%" 2>nul || type "%filepath%" 2>nul
    ) else (
        bat --color=always --style=numbers "%filepath%" 2>nul || type "%filepath%" 2>nul
    )
) else (
    echo File not found: %filepath%
)
